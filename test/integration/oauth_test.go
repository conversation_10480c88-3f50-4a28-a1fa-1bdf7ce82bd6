//go:build integration
// +build integration

package integration

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/luispater/CLIProxyAPI/internal/auth"
	"github.com/luispater/CLIProxyAPI/internal/auth/openai"
	"github.com/luispater/CLIProxyAPI/internal/client"
	"github.com/luispater/CLIProxyAPI/internal/config"
)

// TestOAuthFlowIntegration tests the complete OAuth flow with mock servers
func TestOAuthFlowIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Create temporary directory for auth files
	tempDir, err := os.MkdirTemp("", "openai_auth_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create test configuration
	cfg := &config.Config{
		AuthDir: tempDir,
		OpenAI: config.OpenAIConfig{
			CallbackPort:           3001,
			EnableCreditRedemption: true,
		},
	}

	// Test complete authentication flow
	t.Run("CompleteAuthFlow", func(t *testing.T) {
		// Generate PKCE codes
		pkceCodes, err := openai.GeneratePKCECodes()
		if err != nil {
			t.Fatalf("Failed to generate PKCE codes: %v", err)
		}

		// Create OpenAI auth service
		openaiAuth := openai.NewOpenAIAuth(cfg)

		// Generate auth URL
		state := "test-state-123"
		authURL, err := openaiAuth.GenerateAuthURL(state, pkceCodes)
		if err != nil {
			t.Fatalf("Failed to generate auth URL: %v", err)
		}

		// Verify auth URL structure
		if !strings.Contains(authURL, "client_id=") {
			t.Error("Auth URL missing client_id")
		}
		if !strings.Contains(authURL, "code_challenge=") {
			t.Error("Auth URL missing code_challenge")
		}

		// Simulate token exchange (this would normally happen after user authorization)
		ctx := context.Background()

		// Test token exchange with mock data
		testTokenExchange(t, openaiAuth, ctx, pkceCodes)

		// Test user info retrieval
		testUserInfoRetrieval(t, openaiAuth, ctx)

		// Test token storage and client integration
		testClientIntegration(t, cfg, tempDir)
	})
}

func testTokenExchange(t *testing.T, authService *openai.OpenAIAuth, ctx context.Context, pkceCodes *openai.PKCECodes) {
	// This would normally exchange a real authorization code
	// For testing, we verify the request structure and error handling

	// Test with invalid code (should fail gracefully)
	_, err := authService.ExchangeCodeForTokens(ctx, "invalid-code", "test-state", pkceCodes)
	if err == nil {
		t.Error("Expected error with invalid authorization code")
	}

	// Verify error is network-related, not parameter validation
	if strings.Contains(err.Error(), "PKCE codes are required") {
		t.Error("Should not fail on PKCE validation with valid codes")
	}
}

func testUserInfoRetrieval(t *testing.T, authService *openai.OpenAIAuth, ctx context.Context) {
	// Test with invalid token (should fail gracefully)
	_, err := authService.GetUserInfo(ctx, "invalid-token")
	if err == nil {
		t.Error("Expected error with invalid access token")
	}

	// Test with empty token
	_, err = authService.GetUserInfo(ctx, "")
	if err == nil {
		t.Error("Expected error with empty access token")
	}
	if !strings.Contains(err.Error(), "access token is required") {
		t.Errorf("Error should mention access token requirement: %v", err)
	}
}

func testClientIntegration(t *testing.T, cfg *config.Config, authDir string) {
	// Create OpenAI client
	openaiClient, err := client.NewOpenAIClient(cfg, authDir)
	if err != nil {
		t.Fatalf("Failed to create OpenAI client: %v", err)
	}

	// Test initial state
	if openaiClient.IsAuthenticated() {
		t.Error("Client should not be authenticated initially")
	}

	// Create mock token storage
	mockStorage := &openai.OpenAITokenStorage{
		GeminiTokenStorage: auth.GeminiTokenStorage{
			Email: "<EMAIL>",
			Type:  "openai",
		},
		IDToken:      "mock-id-token",
		AccessToken:  "mock-access-token",
		RefreshToken: "mock-refresh-token",
		AccountID:    "mock-account-id",
		APIKey:       "mock-api-key",
		LastRefresh:  time.Now().Format(time.RFC3339),
	}

	// Test saving token storage
	err = openaiClient.SaveTokenToFile(mockStorage)
	if err != nil {
		t.Fatalf("Failed to save token storage: %v", err)
	}

	// Verify file was created with correct permissions
	tokenFile := openaiClient.GetTokenFilePath()
	info, err := os.Stat(tokenFile)
	if err != nil {
		t.Fatalf("Token file was not created: %v", err)
	}

	// Check file permissions (should be 0600)
	if info.Mode().Perm() != 0600 {
		t.Errorf("Token file permissions = %o, want 0600", info.Mode().Perm())
	}

	// Test authentication status
	status := openaiClient.GetAuthenticationStatus()
	if status["authenticated"] != true {
		t.Error("Authentication status should be true")
	}
	if status["email"] != "<EMAIL>" {
		t.Errorf("Email = %v, want <EMAIL>", status["email"])
	}

	// Test clearing token storage
	err = openaiClient.ClearTokenStorage()
	if err != nil {
		t.Fatalf("Failed to clear token storage: %v", err)
	}

	// Verify file was removed
	if _, err := os.Stat(tokenFile); !os.IsNotExist(err) {
		t.Error("Token file should have been removed")
	}

	// Verify client is no longer authenticated
	if openaiClient.IsAuthenticated() {
		t.Error("Client should not be authenticated after clearing tokens")
	}
}

// TestOAuthServerIntegration tests the OAuth callback server
func TestOAuthServerIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	server := openai.NewOAuthServer(0) // Use system-assigned port
	ctx := context.Background()

	// Start server
	err := server.Start(ctx)
	if err != nil {
		t.Fatalf("Failed to start OAuth server: %v", err)
	}
	defer server.Stop(ctx)

	// Test server is running
	if !server.IsRunning() {
		t.Error("Server should be running")
	}

	// Test timeout behavior
	start := time.Now()
	result, err := server.WaitForCallback(100 * time.Millisecond)
	duration := time.Since(start)

	if err == nil {
		t.Error("Should have timed out")
	}
	if result != nil {
		t.Error("Result should be nil on timeout")
	}
	if duration < 100*time.Millisecond {
		t.Errorf("Should wait at least 100ms, waited %v", duration)
	}
}

// TestFilePermissions tests that token files are created with secure permissions
func TestFilePermissions(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Create temporary directory
	tempDir, err := os.MkdirTemp("", "auth_permissions_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create config
	cfg := &config.Config{
		AuthDir: tempDir,
	}

	// Create client
	openaiClient, err := client.NewOpenAIClient(cfg, tempDir)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	// Create mock token storage
	storage := &openai.OpenAITokenStorage{
		GeminiTokenStorage: auth.GeminiTokenStorage{
			Email: "<EMAIL>",
			Type:  "openai",
		},
		AccessToken: "test-token",
	}

	// Save token storage
	err = openaiClient.SaveTokenToFile(storage)
	if err != nil {
		t.Fatalf("Failed to save token storage: %v", err)
	}

	// Check file permissions
	tokenFile := openaiClient.GetTokenFilePath()
	info, err := os.Stat(tokenFile)
	if err != nil {
		t.Fatalf("Token file not found: %v", err)
	}

	// Verify permissions are 0600 (read/write for owner only)
	expectedPerm := os.FileMode(0600)
	if info.Mode().Perm() != expectedPerm {
		t.Errorf("File permissions = %o, want %o", info.Mode().Perm(), expectedPerm)
	}

	// Verify directory permissions
	dirInfo, err := os.Stat(tempDir)
	if err != nil {
		t.Fatalf("Auth directory not found: %v", err)
	}

	// Directory should be 0700 (read/write/execute for owner only)
	expectedDirPerm := os.FileMode(0700)
	if dirInfo.Mode().Perm() != expectedDirPerm {
		t.Errorf("Directory permissions = %o, want %o", dirInfo.Mode().Perm(), expectedDirPerm)
	}
}

// TestErrorHandling tests error scenarios in the OAuth flow
func TestErrorHandling(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Test PKCE generation
	t.Run("PKCEGeneration", func(t *testing.T) {
		codes, err := openai.GeneratePKCECodes()
		if err != nil {
			t.Fatalf("PKCE generation failed: %v", err)
		}

		if len(codes.CodeVerifier) != 128 {
			t.Errorf("Code verifier length = %d, want 128", len(codes.CodeVerifier))
		}

		if len(codes.CodeChallenge) != 43 {
			t.Errorf("Code challenge length = %d, want 43", len(codes.CodeChallenge))
		}
	})

	// Test JWT parsing
	t.Run("JWTParsing", func(t *testing.T) {
		// Test with invalid JWT
		_, err := openai.ParseJWTToken("invalid.jwt.token")
		if err == nil {
			t.Error("Should fail with invalid JWT")
		}

		// Test with valid JWT structure but invalid content
		validStructure := "eyJhbGciOiJSUzI1NiJ9.eyJzdWIiOiJ0ZXN0In0.signature"
		claims, err := openai.ParseJWTToken(validStructure)
		if err != nil {
			t.Fatalf("Should parse valid JWT structure: %v", err)
		}

		if claims.Subject != "test" {
			t.Errorf("Subject = %s, want test", claims.Subject)
		}
	})

	// Test error types
	t.Run("ErrorTypes", func(t *testing.T) {
		// Test authentication errors
		authErr := openai.NewAuthenticationError(openai.ErrTokenExpired, fmt.Errorf("network error"))
		if !openai.IsAuthenticationError(authErr) {
			t.Error("Should be recognized as authentication error")
		}

		// Test OAuth errors
		oauthErr := openai.NewOAuthError("access_denied", "User denied access", http.StatusBadRequest)
		if !openai.IsOAuthError(oauthErr) {
			t.Error("Should be recognized as OAuth error")
		}

		// Test user-friendly messages
		friendlyMsg := openai.GetUserFriendlyMessage(authErr)
		if !strings.Contains(strings.ToLower(friendlyMsg), "expired") {
			t.Errorf("Friendly message should mention expiration: %s", friendlyMsg)
		}
	})
}
