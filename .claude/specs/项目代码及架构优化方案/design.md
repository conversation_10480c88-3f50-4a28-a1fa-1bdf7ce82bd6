# Project Codebase and Architecture Optimization — Design

## Overview
This design translates the approved requirements into concrete architectural changes, interfaces, and validation strategies for CLI Proxy API. It standardizes package boundaries, hardens critical paths (handlers, auth, client pool, watcher, config), and codifies logging, error handling, and testing practices with minimal disruption and incremental rollout.

## Architecture
- Layers:
  - API Layer: HTTP handlers (OpenAI/Claude/Gemini compatibility) and middleware.
  - Domain Layer: request/response translation, client selection, streaming orchestration.
  - Infrastructure Layer: auth providers, HTTP clients pool, file watcher, config loader, logging.
- Dependency Rules:
  - API -> Domain -> Infrastructure (no upward imports).
  - Cross-package imports only through exported interfaces.
- Package Guardrails:
  - Add static dependency checks (simple script or build tag asserts) to prevent cycles.

```mermaid
flowchart LR
  subgraph API
    H1[handlers: openai] --> D1
    H2[handlers: claude] --> D1
    H3[handlers: gemini] --> D1
  end
  subgraph Domain
    D1[translate + orchestrate] --> I1
    D1 --> I2
    D1 --> I3
  end
  subgraph Infra
    I1[client pool]
    I2[auth providers]
    I3[file watcher]
    I4[config]
    I5[logger]
  end
  I4 --> I1
  I4 --> H1
  I4 --> H2
  I4 --> H3
```

## Components and Interfaces
- Translation Service
  - Responsibility: deterministic param mapping and SSE framing across providers.
  - Interface: `Translate(req any) (providerReq any, stream bool, err error)`; `MapStreamEvents(in <-chan Event) <-chan Event`.
- Client Pool
  - Responsibility: token/key selection, retry/rotation, backoff, failure accounting.
  - Interface: `Acquire(ctx, caps) (Client, release func(error))`; `RecordFailure(clientID, reason)`.
- Auth Providers
  - Responsibility: OAuth2 token refresh, API key validation, permission checks.
  - Interface: `Token(ctx) (string, error)`; `Validate() error`.
- File Watcher
  - Responsibility: debounce, macOS rename handling, reload callbacks.
  - Interface: `Watch(paths, cb func(EventBatch)) error`.
- Config Loader
  - Responsibility: load/validate, expand home, atomic hot-reload, last-good.
  - Interface: `Load(path) (Config, error)`; `Subscribe(func(Config))`.
- Logging
  - Responsibility: structured fields, redaction, request/trace ids.
  - Contract: fields: `op, route, model, status, latency_ms, client_id`.

## Data Models
- Config
  - Fields: `port int`, `debug bool`, `proxy_url string`, `auth_dir string`, `api_keys []string`, `quota_exceeded { retry_policy, backoff }`.
  - Validation: presence, scheme checks, path expansion rules.
- Client
  - Fields: `id string`, `kind enum(oauth, api_key)`, `http *http.Client`, `limits Quota`.
- Event
  - Fields: `type enum(reload, error, rotate)`, `ts time`, `meta map[string]any`.

## Error Handling
- Wrapping: `fmt.Errorf("op %s: %w", op, err)` everywhere outbound.
- Defer Closers: closure with error capture and structured logging per conventions.
- Sentinel classification: quota, auth, transient, permanent.
- Surfacing: last-good config on invalid reload; aggregated reasons on pool exhaustion.

## Logging Strategy
- Redaction: tokens/keys/PII masked at sink.
- Request Logs: `op=handle_chat route=/v1/... model=... status=... latency_ms=...`.
- Pool Events: `op=client_rotate size=... chosen=... reason=...` at info/debug.
- Watcher: `op=fs_reload path=... kind=RENAME/WRITE debounced=true`.

## Testing Strategy
- Unit
  - Translation: param mapping and SSE framing golden tests (OpenAI/Claude/Gemini).
  - Watcher: debounce + macOS rename synthetic sequences.
  - Pool: retry/rotation/backoff with fake clock.
- Integration (mocked providers)
  - Chat completion (non-stream + stream) across providers with normalized expectations.
  - Config hot-reload applying last-good on invalid updates.
- Tooling
  - Static analysis: go vet, gofmt, golangci-lint profile aligned with repo.
  - Dependency guard: simple check script run in CI to detect cycles/banned edges.

## Incremental Rollout
- Phase 1: add interfaces and adapters; keep existing impl behind facades.
- Phase 2: migrate handlers to translation service; add structured fields.
- Phase 3: harden pool with backoff + failure accounting; integrate watcher debounce.
- Phase 4: enable static checks and finalize tests; remove deprecated paths.

## Risks and Mitigations
- Risk: Behavior drift during translation refactor → Golden tests and shadow mode logging.
- Risk: Hot-reload instability → last-good config with atomic swap and validation.
- Risk: Over-logging secrets → centralized redactor and field allowlist.
