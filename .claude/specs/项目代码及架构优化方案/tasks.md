# Project Codebase and Architecture Optimization — Implementation Tasks

Note: Tasks are actionable coding steps only. Each task references specific requirement IDs from requirements.md.

1. [ ] Introduce translation service façade
   - Implement `Translate` and SSE mapping with golden tests.
   - Files: `internal/translate/` (new), adapt handlers to call façade only.
   - Covers: Req 7.2(1), 10.2(1)

1.1 [ ] Add OpenAI parameter mapping tests
   - Golden inputs/outputs; stream/non-stream.
   - Covers: Req 7.2(1), 10.2(1)

1.2 [ ] Add Claude parameter mapping tests
   - Golden inputs/outputs; SSE ordering.
   - Covers: Req 7.2(1), 7.2(3)

1.3 [ ] Add Gemini parameter mapping tests
   - Golden inputs/outputs; streaming chunking.
   - Covers: Req 7.2(1), 7.2(3)

2. [ ] Standardize error handling wrappers
   - Add helper: `operr.Wrap(op, err)`; migrate callers.
   - Files: `internal/x/errx/` (new) + repo-wide small changes.
   - Covers: Req 2.2(1), 2.2(3)

3. [ ] Defer-closer logging utility
   - Helper `deferclose.Do(closer, logger, op)` to log close errors.
   - Files: `internal/x/closex/` (new); refactor usage in handlers and IO.
   - Covers: Req 2.2(2)

4. [ ] Structured logging fields and redaction
   - Centralize logger with field allowlist and redaction.
   - Files: `internal/logx/`; integrate in server startup.
   - Covers: Req 3.2(1), 8.2(1)

5. [ ] Client pool backoff and rotation
   - Add failure accounting, retry policy, acquire/release contract.
   - Files: `internal/client/pool.go` updates + tests with fake clock.
   - Covers: Req 6.2(1), 6.2(2), 10.2(2)

6. [ ] Config loader validation and last-good swap
   - Expand `~`, validate fields, atomic hot-reload with subscribe.
   - Files: `internal/config/loader.go` + tests.
   - Covers: Req 4.2(1-3)

7. [ ] File watcher debounce + macOS rename handling
   - Coalesce bursts; re-establish watches on RENAME/DELETE.
   - Files: `internal/watcher/watcher.go` + tests with synthetic events.
   - Covers: Req 5.2(1-3), 10.2(1)

8. [ ] Dependency guard script
   - Simple check preventing cycles/forbidden edges run in CI.
   - Files: `tools/depguard/main.go` or script.
   - Covers: Req 1.2(1-3), 11.2(2)

9. [ ] Handler integration with façade and logging
   - Refactor handlers to use translate façade and structured logs.
   - Files: `internal/api/*handlers.go`.
   - Covers: Req 3.2(1), 7.2(1-3)

10. [ ] Performance sanity and resource limits
    - Bounded goroutines; O(1) per chunk streaming.
    - Files: streaming paths in translate/handlers; benchmarks optional.
    - Covers: Req 9.2(2-3)

11. [ ] Add unit and integration test suites
    - Mocks for providers, fake clock/time, golden data.
    - Files: `internal/**/_test.go`, `testdata/`.
    - Covers: Req 10.2(1-3)

12. [ ] Build tooling hooks
    - Ensure gofmt, govet, golangci-lint profiles, and guard in CI.
    - Files: `.golangci.yml`, `Makefile`/scripts (reuse existing patterns).
    - Covers: Req 11.2(1-3)

13. [ ] Minimal docs updates
    - Update CLAUDE.md conventions where needed; config examples.
    - Files: `CLAUDE.md`, `config.yaml` examples.
    - Covers: Req 12.2(1,3)

14. [ ] Incremental migration cleanup
    - Remove deprecated paths after green builds; adapters in interim.
    - Files: affected packages post-migration.
    - Covers: Req 13.2(1-3)
