# Project Codebase and Architecture Optimization — Requirements

## Introduction
This initiative aims to improve maintainability, reliability, performance, and developer ergonomics of the CLI Proxy API by standardizing architecture boundaries, hardening critical subsystems (API handlers, auth, client pool, watcher, config), and ensuring consistent error handling, observability, and testing across the codebase.

## Requirements

1. Architecture Boundaries
   1.1 User story: As a maintainer, I want clear module boundaries, so that changes are localized and risk is reduced.
   1.2 Acceptance criteria (EARS):
       1) When adding new handlers, the system shall forbid cross-package imports that violate API/auth/client/config package boundaries.
       2) When building, the system shall enforce cyclic-dependency checks for internal packages.
       3) When running static analysis, the system shall report any forbidden dependency edges with actionable messages.

2. Error Handling Consistency
   2.1 User story: As a developer, I want uniform error handling patterns, so that failures are predictable and debuggable.
   2.2 Acceptance criteria (EARS):
       1) When returning errors, the system shall wrap errors with context using fmt.Errorf or errors.Join as appropriate.
       2) When deferring closers that may error, the system shall log close errors within a closure using project conventions.
       3) When logging errors, the system shall include operation, identifiers, and relevant counts without leaking secrets.

3. Logging and Observability
   3.1 User story: As an operator, I want consistent structured logs and basic metrics, so that I can diagnose incidents quickly.
   3.2 Acceptance criteria (EARS):
       1) When handling requests, the system shall emit structured logs (request id, route, model, latency, status).
       2) When client rotation occurs, the system shall log pool size, selected client id, and failure causes at debug/info.
       3) When watcher reloads config/auth, the system shall log event type, path, and result including debounce decisions.

4. Configuration Management
   4.1 User story: As an operator, I want robust config loading and hot-reload, so that changes apply without restarts.
   4.2 Acceptance criteria (EARS):
       1) When loading config, the system shall expand home directories and validate required fields with clear errors.
       2) When config files change, the system shall debounce events and apply updates atomically without race conditions.
       3) When invalid config is detected at runtime, the system shall reject it and continue running with last-good config.

5. File Watcher Robustness
   5.1 User story: As a maintainer, I want reliable, cross-platform file watching, so that auth/config changes are detected.
   5.2 Acceptance criteria (EARS):
       1) When macOS RENAME/DELETE events occur, the system shall re-establish watches and rescan the target directory.
       2) When bursts of events occur, the system shall coalesce them to a single reload within a bounded interval.
       3) When watch initialization fails, the system shall surface actionable errors and retry with backoff.

6. Client Pool Resilience
   6.1 User story: As a user, I want reliable request handling across accounts, so that quota limits and failures are mitigated.
   6.2 Acceptance criteria (EARS):
       1) When a request fails due to quota or transient errors, the system shall retry on a different eligible client respecting limits.
       2) When tokens expire, the system shall refresh or rotate clients without affecting in-flight unrelated requests.
       3) When all clients are exhausted, the system shall return a clear error including failure reasons summary.

7. API Compatibility
   7.1 User story: As an integrating developer, I want accurate OpenAI/Claude/Gemini compatibility, so that standard tools work.
   7.2 Acceptance criteria (EARS):
       1) When translating requests, the system shall map parameters and streaming semantics precisely per target API.
       2) When listing models, the system shall return normalized model metadata with stable identifiers.
       3) When streaming responses, the system shall ensure event ordering and SSE framing match client expectations.

8. Security and Secrets Hygiene
   8.1 User story: As a security engineer, I want safe handling of credentials, so that secrets are not exposed.
   8.2 Acceptance criteria (EARS):
       1) When logging, the system shall redact tokens, API keys, and PII by default.
       2) When reading auth files, the system shall validate permissions and reject world-readable credentials with guidance.
       3) When using proxies, the system shall validate scheme and avoid logging proxy credentials.

9. Performance and Resource Usage
   9.1 User story: As an operator, I want efficient throughput and memory use, so that the service runs cost-effectively.
   9.2 Acceptance criteria (EARS):
       1) When under load, the system shall keep p50/p95 request latencies within configured SLOs on reference hardware.
       2) When streaming, the system shall allocate O(1) memory per chunk and avoid unbounded buffers.
       3) When idle, the system shall keep background goroutines bounded and observable.

10. Testing Strategy
    10.1 User story: As a maintainer, I want confidence via tests, so that refactors are safe.
    10.2 Acceptance criteria (EARS):
        1) When building, the system shall provide unit tests for translation, watcher debounce, and client rotation.
        2) When running integration tests, the system shall mock API providers to verify compatibility paths deterministically.
        3) When encountering flaky tests, the system shall provide deterministic time control and retry mechanisms.

11. Build and Tooling
    11.1 User story: As a contributor, I want fast builds and linting, so that feedback loops are short.
    11.2 Acceptance criteria (EARS):
        1) When running static analysis, the system shall enforce gofmt/go vet/golangci-lint rules aligned with project conventions.
        2) When running make/CI, the system shall build the server and run unit tests with cache-friendly settings.
        3) When formatting, the system shall apply consistent import grouping and error variable naming.

12. Documentation Minimalism
    12.1 User story: As a developer, I want concise contributor docs, so that onboarding is quick.
    12.2 Acceptance criteria (EARS):
        1) When updating conventions, the system shall document Go error/logging patterns in CLAUDE.md only where necessary.
        2) When adding new packages, the system shall include brief README only if usage is non-obvious.
        3) When adding config fields, the system shall update config examples and validation messages.

13. Migration Safety
    13.1 User story: As a maintainer, I want incremental, reversible changes, so that rollbacks are straightforward.
    13.2 Acceptance criteria (EARS):
        1) When refactoring packages, the system shall perform change sets in small steps with green builds between.
        2) When moving types or interfaces, the system shall provide adapters until dependent modules are updated.
        3) When removing deprecated paths, the system shall document replacements and provide deprecation windows.
