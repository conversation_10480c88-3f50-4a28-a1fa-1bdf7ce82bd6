# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is **CLI Proxy API** - a proxy server that provides OpenAI/Gemini/Claude compatible API interfaces for CLI models. It allows using CLI models with tools and libraries designed for standard AI APIs through HTTP endpoints.

## Build and Development Commands

### Building
```bash
# Build the main server binary
go build -o cli-proxy-api ./cmd/server

# Build with output file for testing (as per project conventions)
go build -o test-output ./cmd/server
rm test-output  # Always clean up after test builds
```

### Running
```bash
# Login to Google account (required before first use)
./cli-proxy-api --login

# Login with specific project ID (for legacy gemini code users)
./cli-proxy-api --login --project_id <your_project_id>

# Start the server (default port 8317)
./cli-proxy-api

# Start with custom config file
./cli-proxy-api --config /path/to/config.yaml
```

### Docker
```bash
# Login via Docker
docker run --rm -p 8085:8085 -v /path/to/config.yaml:/CLIProxyAPI/config.yaml -v /path/to/auth-dir:/root/.cli-proxy-api eceasy/cli-proxy-api:latest /CLIProxyAPI/CLIProxyAPI --login

# Run server via Docker
docker run --rm -p 8317:8317 -v /path/to/config.yaml:/CLIProxyAPI/config.yaml -v /path/to/auth-dir:/root/.cli-proxy-api eceasy/cli-proxy-api:latest
```

## Architecture Overview

### Core Components

1. **HTTP API Server** (`internal/api/server.go`)
   - Gin-based web server with middleware for CORS and authentication
   - Serves OpenAI-compatible (`/v1/*`) and Gemini-compatible (`/v1beta/*`) endpoints
   - Special CLI endpoint (`/v1internal:method`) for Gemini CLI integration

2. **Authentication System** (`internal/auth/`)
   - OAuth2 flow for Google account authentication
   - Token storage and refresh management in JSON files
   - Proxy support (SOCKS5/HTTP/HTTPS)

3. **Client Management** (`internal/client/`)
   - Pool of authenticated HTTP clients for load balancing
   - Supports both OAuth2 tokens and Generative Language API keys
   - Automatic client rotation and failover

4. **File Watcher** (`internal/watcher/`)
   - Real-time monitoring of config and auth directory changes
   - Automatic client reload on auth file modifications
   - Handles platform-specific file system events (especially macOS RENAME events)

5. **Configuration System** (`internal/config/`)
   - YAML-based configuration with home directory expansion
   - Hot-reloading capabilities via file watcher

### API Handlers

The system supports multiple API formats:
- **OpenAI Compatible** (`internal/api/handlers.go`): `/v1/chat/completions`, `/v1/models`
- **Claude Compatible** (`internal/api/claude-code-handlers.go`): `/v1/messages`
- **Gemini Compatible** (`internal/api/gemini-handlers.go`): `/v1beta/models/:action`
- **CLI Integration** (`internal/api/cli-handlers.go`): Special endpoints for Gemini CLI

### Authentication Flow

1. **OAuth2 Login**: Users authenticate via web browser using Google OAuth2
2. **Token Storage**: Tokens stored as JSON files in auth directory (default: `~/.cli-proxy-api`)
3. **Load Balancing**: Multiple token files enable multi-account load balancing
4. **API Key Support**: Alternative authentication via Generative Language API keys

### Configuration Architecture

- **Config File**: YAML format with sensible defaults
- **Auth Directory**: Configurable location for token storage
- **Hot Reloading**: Automatic detection of config and auth changes
- **Proxy Support**: Configurable proxy for outbound requests

## Go Code Conventions

This project follows specific Go coding standards:

### Error Handling
- Use descriptive variable names for errors: `errNewWatcher`, `errLoadConfig`, `errSetProxy`
- Handle errors in defer statements with closures when needed:
```go
defer func() {
    errClose := f.Close()
    if errClose != nil {
        log.Errorf("failed to close file: %v", errClose)
    }
}()
```

### Logging
- Use structured logging with logrus
- Include context in debug messages with timestamps and operation details
- Use appropriate log levels (Debug, Info, Error) based on configuration

### File Structure
- Modern Go project layout with `cmd/` and `internal/` directories
- Separation of concerns: API handlers, auth, client management, config, utilities
- Clear package boundaries with minimal cross-dependencies

## Key Implementation Details

### File Watching
The watcher system handles cross-platform file events, with special handling for macOS where file deletion triggers RENAME events instead of REMOVE events.

### Multi-Account Support
The system can load balance across multiple Google accounts by storing separate token files in the auth directory.

### API Compatibility
Request/response translation between different AI API formats (OpenAI, Claude, Gemini) to provide unified access.

### Proxy Configuration
Supports SOCKS5, HTTP, and HTTPS proxies for environments requiring network routing.

## Configuration Management

- **Default Config Path**: `./config.yaml` in working directory
- **Auth Directory**: `~/.cli-proxy-api` (supports tilde expansion)
- **Port**: 8317 (default)
- **Debug Mode**: Configurable via YAML (`debug: true/false`)

Key configuration sections:
- `api-keys`: For local authentication
- `generative-language-api-key`: For official Gemini API access
- `quota-exceeded`: Automatic failover behavior
- `proxy-url`: Network proxy configuration