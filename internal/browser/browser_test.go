package browser

import (
	"runtime"
	"testing"
)

func TestIsAvailable(t *testing.T) {
	// This test checks if browser functionality is available on the current system
	available := IsAvailable()

	// We can't assert a specific value since it depends on the system,
	// but we can verify the function doesn't panic
	t.Logf("Browser availability on %s: %v", runtime.GOOS, available)
}

func TestGetPlatformInfo(t *testing.T) {
	info := GetPlatformInfo()

	// Verify required fields are present
	if info["os"] == nil {
		t.Error("Platform info missing 'os' field")
	}
	if info["arch"] == nil {
		t.Error("Platform info missing 'arch' field")
	}
	if info["available"] == nil {
		t.Error("Platform info missing 'available' field")
	}

	// Verify OS matches runtime
	if info["os"] != runtime.GOOS {
		t.Errorf("OS mismatch: got %v, want %s", info["os"], runtime.GOOS)
	}

	// Verify arch matches runtime
	if info["arch"] != runtime.GOARCH {
		t.<PERSON>rf("Arch mismatch: got %v, want %s", info["arch"], runtime.GOARCH)
	}

	t.Logf("Platform info: %+v", info)
}

func TestOpenURLInvalidURL(t *testing.T) {
	// Test with an invalid URL - this should not panic
	// but may return an error depending on the system
	err := OpenURL("invalid-url")

	// We don't assert success/failure since it's system-dependent
	// Just verify it doesn't panic
	t.Logf("OpenURL with invalid URL returned: %v", err)
}

func TestOpenURLPlatformSpecific(t *testing.T) {
	// Test platform-specific URL opening with a safe URL
	err := openURLPlatformSpecific("about:blank")

	// This may succeed or fail depending on the system configuration
	// We're mainly testing that it doesn't panic
	t.Logf("Platform-specific open returned: %v", err)
}

// TestBrowserCommands tests the availability of browser commands on different platforms
func TestBrowserCommands(t *testing.T) {
	switch runtime.GOOS {
	case "darwin":
		t.Log("Testing macOS browser commands")
		// On macOS, 'open' command should be available

	case "windows":
		t.Log("Testing Windows browser commands")
		// On Windows, 'rundll32' should be available

	case "linux":
		t.Log("Testing Linux browser commands")
		// On Linux, at least one browser command should be available

	default:
		t.Logf("Testing on unsupported OS: %s", runtime.GOOS)
	}

	// Get platform info to see what's available
	info := GetPlatformInfo()
	t.Logf("Available browser info: %+v", info)
}
