package openai

import (
	"encoding/json"
	"reflect"
	"testing"
)

func normalizeJSON(t *testing.T, s string) any {
	t.Helper()
	var v any
	if err := json.Unmarshal([]byte(s), &v); err != nil {
		t.Fatalf("invalid json: %v\n%s", err, s)
	}
	return v
}

func TestConvert_1_SimpleDialog(t *testing.T) {
	in := `{
        "stream": true,
        "temperature": 0.75,
        "top_p": 0.9,
        "max_tokens": 2000,
        "reasoning_effort": "low",
        "model": "gpt-5",
        "messages": [
          {"role": "system", "content": "You are a helpful assistant."},
          {"role": "user", "content": "Hello!"}
        ]
      }`

	want := `{
        "stream": true,
        "temperature": 0.75,
        "top_p": 0.9,
        "max_output_tokens": 2000,
        "reasoning": {"effort": "low"},
        "model": "gpt-5",
        "instructions": "You are a helpful assistant.",
        "input": [
          {"role": "user", "content": [{"type": "input_text", "text": "Hello!"}]}
        ]
      }`

	got := ConvertOpenAIChatRequestToCodex([]byte(in))
	if !reflect.DeepEqual(normalizeJSON(t, got), normalizeJSON(t, want)) {
		t.Fatalf("mismatch\nwant: %s\n got: %s", want, got)
	}
}

func TestConvert_2_MultiTurn(t *testing.T) {
	in := `{
        "model": "gpt-5",
        "max_completion_tokens": 2000,
        "messages": [
          {"role": "system", "content": "You are a helpful assistant."},
          {"role": "user", "content": "Hello!"},
          {"role": "assistant", "content": "Hello too!"},
          {"role": "user", "content": "Who are you?"}
        ]
      }`

	want := `{
        "model": "gpt-5",
        "max_output_tokens": 2000,
        "instructions": "You are a helpful assistant.",
        "input": [
          {"role": "user", "content": [{"type": "input_text", "text": "Hello!"}]},
          {"role": "assistant", "content": [{"type": "output_text", "text": "Hello too!"}]},
          {"role": "user", "content": [{"type": "input_text", "text": "Who are you?"}]}
        ]
      }`

	got := ConvertOpenAIChatRequestToCodex([]byte(in))
	if !reflect.DeepEqual(normalizeJSON(t, got), normalizeJSON(t, want)) {
		t.Fatalf("mismatch\nwant: %s\n got: %s", want, got)
	}
}

func TestConvert_3_WithTools_TextFormat(t *testing.T) {
	in := `{
      "model": "gpt-5",
      "messages": [
        {"role": "user", "content": [{"type": "text", "text": "hello"}]},
        {"role": "assistant", "content": [{"type": "text", "text": "hello too"}]},
        {"role": "user", "content": [{"type": "text", "text": "who are you?"}]}
      ],
      "response_format": {"type": "text"},
      "text": {"verbosity": "medium"},
      "reasoning_effort": "medium",
      "tools": [
        {"type": "function", "function": {
          "name": "get_current_weather",
          "description": "Get the current weather in a given location",
          "parameters": {"type": "object", "properties": {
            "location": {"type": "string", "description": "The city and state, e.g. San Francisco, CA"},
            "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]}
          }, "required": ["location"]},
          "strict": false
        }}
      ]
    }`

	want := `{
      "model": "gpt-5",
      "input": [
        {"role": "user", "content": [{"type": "input_text", "text": "hello"}]},
        {"role": "assistant", "content": [{"type": "output_text", "text": "hello too"}]},
        {"role": "user", "content": [{"type": "input_text", "text": "who are you?"}]}
      ],
      "text": {"format": {"type": "text"}, "verbosity": "medium"},
      "reasoning": {"effort": "medium"},
      "tools": [
        {"type": "function", "name": "get_current_weather", "description": "Get the current weather in a given location",
          "parameters": {"type": "object", "properties": {
            "location": {"type": "string", "description": "The city and state, e.g. San Francisco, CA"},
            "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]}
          }, "required": ["location"]},
          "strict": false
        }
      ],
      "store": true
    }`

	got := ConvertOpenAIChatRequestToCodex([]byte(in))
	if !reflect.DeepEqual(normalizeJSON(t, got), normalizeJSON(t, want)) {
		t.Fatalf("mismatch\nwant: %s\n got: %s", want, got)
	}
}

func TestConvert_4_StructuredOutputs_JSONSchema(t *testing.T) {
	in := `{
      "model": "gpt-5",
      "messages": [
        {"role": "user", "content": [{"type": "text", "text": "hello"}]},
        {"role": "assistant", "content": [{"type": "text", "text": "hello too"}]},
        {"role": "user", "content": [{"type": "text", "text": "who are you?"}]}
      ],
      "response_format": {
        "type": "json_schema",
        "json_schema": {
          "name": "math_response",
          "strict": true,
          "schema": {
            "type": "object",
            "properties": {
              "steps": {"type": "array", "items": {
                "type": "object",
                "properties": {"explanation": {"type": "string"}, "output": {"type": "string"}},
                "required": ["explanation", "output"],
                "additionalProperties": false
              }},
              "final_answer": {"type": "string"}
            },
            "additionalProperties": false,
            "required": ["steps", "final_answer"]
          }
        }
      },
      "text": {"verbosity": "medium"},
      "reasoning_effort": "medium",
      "tools": [
        {"type": "function", "function": {
          "name": "get_current_weather",
          "description": "Get the current weather in a given location",
          "parameters": {"type": "object", "properties": {
            "location": {"type": "string", "description": "The city and state, e.g. San Francisco, CA"},
            "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]}
          }, "required": ["location"]},
          "strict": false
        }}
      ]
    }`

	want := `{
      "model": "gpt-5",
      "input": [
        {"role": "user", "content": [{"type": "input_text", "text": "hello"}]},
        {"role": "assistant", "content": [{"type": "output_text", "text": "hello too"}]},
        {"role": "user", "content": [{"type": "input_text", "text": "who are you?"}]}
      ],
      "text": {
        "format": {
          "type": "json_schema",
          "name": "math_response",
          "strict": true,
          "schema": {
            "type": "object",
            "properties": {
              "steps": {"type": "array", "items": {
                "type": "object",
                "properties": {"explanation": {"type": "string"}, "output": {"type": "string"}},
                "required": ["explanation", "output"],
                "additionalProperties": false
              }},
              "final_answer": {"type": "string"}
            },
            "additionalProperties": false,
            "required": ["steps", "final_answer"]
          }
        },
        "verbosity": "medium"
      },
      "reasoning": {"effort": "medium"},
      "tools": [
        {"type": "function", "name": "get_current_weather", "description": "Get the current weather in a given location",
          "parameters": {"type": "object", "properties": {
            "location": {"type": "string", "description": "The city and state, e.g. San Francisco, CA"},
            "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]}
          }, "required": ["location"]},
          "strict": false
        }
      ],
      "store": true
    }`

	got := ConvertOpenAIChatRequestToCodex([]byte(in))
	if !reflect.DeepEqual(normalizeJSON(t, got), normalizeJSON(t, want)) {
		t.Fatalf("mismatch\nwant: %s\n got: %s", want, got)
	}
}

func TestConvert_5_ImageInput(t *testing.T) {
	in := `{
      "model": "gpt-4.1",
      "messages": [
        {"role": "user", "content": [
          {"type": "text", "text": "What is in this image?"},
          {"type": "image_url", "image_url": {"url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"}}
        ]}
      ]
    }`

	// Note: docs example shows lowercase in converted text; we preserve original text casing.
	want := `{
      "model": "gpt-4.1",
      "input": [
        {"role": "user", "content": [
          {"type": "input_text", "text": "What is in this image?"},
          {"type": "input_image", "image_url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"}
        ]}
      ]
    }`

	got := ConvertOpenAIChatRequestToCodex([]byte(in))
	if !reflect.DeepEqual(normalizeJSON(t, got), normalizeJSON(t, want)) {
		t.Fatalf("mismatch\nwant: %s\n got: %s", want, got)
	}
}
