// Package code provides response translation functionality for Gemini API.
// This package handles the conversion of Codex backend responses into Gemini-compatible
// JSON format, transforming streaming events into single-line JSON responses that include
// thinking content, regular text content, and function calls in the format expected by
// Gemini API clients.
package code

import (
	"time"

	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
)

// ConvertCodexResponseToGemini converts Codex streaming response format to Gemini single-line JSON format.
// This function processes various Codex event types and transforms them into Gemini-compatible JSON responses.
// It handles thinking content, regular text content, and function calls, outputting single-line JSON
// that matches the Gemini API response format.
func ConvertCodexResponseToGemini(rawJSON []byte, model string, createdAt int64) (string, int64) {
	rootResult := gjson.ParseBytes(rawJSON)
	typeResult := rootResult.Get("type")
	typeStr := typeResult.String()

	// Base Gemini response template
	template := `{"candidates":[{"content":{"role":"model","parts":[]}}],"usageMetadata":{"trafficType":"PROVISIONED_THROUGHPUT"},"modelVersion":"gemini-2.5-pro","createTime":"2025-08-15T02:52:03.884209Z","responseId":"06CeaPH7NaCU48APvNXDyA4"}`
	template, _ = sjson.Set(template, "modelVersion", model)

	createdAtResult := rootResult.Get("response.created_at")
	if createdAtResult.Exists() {
		createdAt = createdAtResult.Int()
		template, _ = sjson.Set(template, "createTime", time.Unix(createdAt, 0).Format(time.RFC3339Nano))
	}
	// Handle response creation - set model and response ID
	if typeStr == "response.created" {
		template, _ = sjson.Set(template, "modelVersion", rootResult.Get("response.model").String())
		template, _ = sjson.Set(template, "responseId", rootResult.Get("response.id").String())
		return template, createdAt
	}

	// Handle reasoning/thinking content start
	if typeStr == "response.reasoning_summary_part.added" {
		// Start of thinking content - we'll accumulate the text in delta events
		return "", createdAt
	}

	// Handle reasoning/thinking content delta
	if typeStr == "response.reasoning_summary_text.delta" {
		part := `{"thought":true,"text":""}`
		part, _ = sjson.Set(part, "text", rootResult.Get("delta").String())
		template, _ = sjson.SetRaw(template, "candidates.0.content.parts.-1", part)
		return template, createdAt
	}

	// Handle reasoning/thinking content completion
	if typeStr == "response.reasoning_summary_part.done" {
		// End of thinking content - no additional output needed
		return "", createdAt
	}

	// Handle content part start
	if typeStr == "response.content_part.added" {
		// Start of content part - we'll handle the text in delta events
		return "", createdAt
	}

	// Handle regular text content delta
	if typeStr == "response.output_text.delta" {
		part := `{"text":""}`
		part, _ = sjson.Set(part, "text", rootResult.Get("delta").String())
		template, _ = sjson.SetRaw(template, "candidates.0.content.parts.-1", part)
		return template, createdAt
	}

	// Handle content part completion
	if typeStr == "response.content_part.done" {
		// End of content part - no additional output needed
		return "", createdAt
	}

	// Handle output text completion
	if typeStr == "response.output_text.done" {
		// End of text output - no additional output needed
		return "", createdAt
	}

	// Handle function call start
	if typeStr == "response.output_item.added" {
		itemResult := rootResult.Get("item")
		itemType := itemResult.Get("type").String()
		if itemType == "function_call" {
			// For function call start, we don't output anything yet
			// Wait for the completion to output the full function call
			return "", createdAt
		}
	}

	// Handle function call completion
	if typeStr == "response.output_item.done" {
		itemResult := rootResult.Get("item")
		itemType := itemResult.Get("type").String()
		if itemType == "function_call" {
			// Create function call part with thoughtSignature
			functionCall := `{"functionCall":{"name":"","args":{}}}`
			functionCall, _ = sjson.Set(functionCall, "functionCall.name", itemResult.Get("name").String())

			// Parse and set arguments
			argsStr := itemResult.Get("arguments").String()
			if argsStr != "" {
				argsResult := gjson.Parse(argsStr)
				if argsResult.IsObject() {
					functionCall, _ = sjson.SetRaw(functionCall, "functionCall.args", argsStr)
				}
			}

			template, _ = sjson.SetRaw(template, "candidates.0.content.parts.-1", functionCall)
			template, _ = sjson.Set(template, "candidates.0.finishReason", "STOP")
			return template, createdAt
		}
	}

	// Handle function call arguments delta (streaming function arguments)
	if typeStr == "response.function_call_arguments.delta" {
		// For Gemini format, we don't stream function arguments
		// We wait for the complete function call in response.output_item.done
		return "", createdAt
	}

	// Handle function call arguments completion
	if typeStr == "response.function_call_arguments.done" {
		// Function arguments are complete, but we still wait for output_item.done
		return "", createdAt
	}

	// Handle response in progress
	if typeStr == "response.in_progress" {
		// No output needed for progress updates
		return "", createdAt
	}

	// Handle response completion with usage metadata
	if typeStr == "response.completed" {
		template, _ = sjson.Set(template, "usageMetadata.promptTokenCount", rootResult.Get("response.usage.input_tokens").Int())
		template, _ = sjson.Set(template, "usageMetadata.candidatesTokenCount", rootResult.Get("response.usage.output_tokens").Int())
		totalTokens := rootResult.Get("response.usage.input_tokens").Int() + rootResult.Get("response.usage.output_tokens").Int()
		template, _ = sjson.Set(template, "usageMetadata.totalTokenCount", totalTokens)
		return template, createdAt
	}

	// Return empty string for unhandled event types
	return "", createdAt
}

func ConvertCodexResponseToGeminiNonStream(rawJSON []byte, model string) string {
	return ""
}
