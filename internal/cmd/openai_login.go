package cmd

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/luispater/CLIProxyAPI/internal/auth/openai"
	"github.com/luispater/CLIProxyAPI/internal/browser"
	"github.com/luispater/CLIProxyAPI/internal/client"
	"github.com/luispater/CLIProxyAPI/internal/config"
	log "github.com/sirupsen/logrus"
)

// LoginOptions contains options for OpenAI login
type LoginOptions struct {
	NoBrowser bool
}

// DoOpenAILogin handles the OpenAI OAuth login process
func DoOpenAILogin(cfg *config.Config, options *LoginOptions) {
	if options == nil {
		options = &LoginOptions{}
	}

	ctx := context.Background()

	log.Info("Initializing OpenAI authentication...")

	// Generate PKCE codes
	pkceCodes, err := openai.GeneratePKCECodes()
	if err != nil {
		log.Fatalf("Failed to generate PKCE codes: %v", err)
		return
	}

	// Generate random state parameter
	state, err := generateRandomState()
	if err != nil {
		log.Fatalf("Failed to generate state parameter: %v", err)
		return
	}

	// Initialize OAuth server
	oauthServer := openai.NewOAuthServer(1455)

	// Start OAuth callback server
	if err = oauthServer.Start(ctx); err != nil {
		if strings.Contains(err.Error(), "already in use") {
			authErr := openai.NewAuthenticationError(openai.ErrPortInUse, err)
			log.Error(openai.GetUserFriendlyMessage(authErr))
			os.Exit(13) // Exit code 13 for port-in-use error
		}
		authErr := openai.NewAuthenticationError(openai.ErrServerStartFailed, err)
		log.Fatalf("Failed to start OAuth callback server: %v", authErr)
		return
	}
	defer func() {
		if err = oauthServer.Stop(ctx); err != nil {
			log.Warnf("Failed to stop OAuth server: %v", err)
		}
	}()

	// Initialize OpenAI auth service
	openaiAuth := openai.NewOpenAIAuth(cfg)

	// Generate authorization URL
	authURL, err := openaiAuth.GenerateAuthURL(state, pkceCodes)
	if err != nil {
		log.Fatalf("Failed to generate authorization URL: %v", err)
		return
	}

	// Open browser or display URL
	if !options.NoBrowser {
		log.Info("Opening browser for authentication...")

		// Check if browser is available
		if !browser.IsAvailable() {
			log.Warn("No browser available on this system")
			log.Infof("Please manually open this URL in your browser:\n\n%s\n", authURL)
		} else {
			if err = browser.OpenURL(authURL); err != nil {
				authErr := openai.NewAuthenticationError(openai.ErrBrowserOpenFailed, err)
				log.Warn(openai.GetUserFriendlyMessage(authErr))
				log.Infof("Please manually open this URL in your browser:\n\n%s\n", authURL)

				// Log platform info for debugging
				platformInfo := browser.GetPlatformInfo()
				log.Debugf("Browser platform info: %+v", platformInfo)
			} else {
				log.Debug("Browser opened successfully")
			}
		}
	} else {
		log.Infof("Please open this URL in your browser:\n\n%s\n", authURL)
	}

	log.Info("Waiting for authentication callback...")

	// Wait for OAuth callback
	result, err := oauthServer.WaitForCallback(5 * time.Minute)
	if err != nil {
		if strings.Contains(err.Error(), "timeout") {
			authErr := openai.NewAuthenticationError(openai.ErrCallbackTimeout, err)
			log.Error(openai.GetUserFriendlyMessage(authErr))
		} else {
			log.Errorf("Authentication failed: %v", err)
		}
		return
	}

	if result.Error != "" {
		oauthErr := openai.NewOAuthError(result.Error, "", http.StatusBadRequest)
		log.Error(openai.GetUserFriendlyMessage(oauthErr))
		return
	}

	// Validate state parameter
	if result.State != state {
		authErr := openai.NewAuthenticationError(openai.ErrInvalidState, fmt.Errorf("expected %s, got %s", state, result.State))
		log.Error(openai.GetUserFriendlyMessage(authErr))
		return
	}

	log.Debug("Authorization code received, exchanging for tokens...")

	// Exchange authorization code for tokens
	authBundle, err := openaiAuth.ExchangeCodeForTokens(ctx, result.Code, pkceCodes)
	if err != nil {
		authErr := openai.NewAuthenticationError(openai.ErrCodeExchangeFailed, err)
		log.Errorf("Failed to exchange authorization code for tokens: %v", authErr)
		log.Debug("This may be due to network issues or invalid authorization code")
		return
	}

	// Create token storage
	tokenStorage := openaiAuth.CreateTokenStorage(authBundle)

	// Initialize OpenAI client
	openaiClient, err := client.NewOpenAIClient(cfg, tokenStorage)
	if err != nil {
		log.Fatalf("Failed to initialize OpenAI client: %v", err)
		return
	}

	// Save token storage
	if err = openaiClient.SaveTokenToFile(); err != nil {
		log.Fatalf("Failed to save authentication tokens: %v", err)
		return
	}

	log.Info("Authentication successful!")
	if authBundle.APIKey != "" {
		log.Info("API key obtained and saved")
	}

	log.Info("You can now use OpenAI services through this CLI")
}

// generateRandomState generates a cryptographically secure random state parameter
func generateRandomState() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}
	return hex.EncodeToString(bytes), nil
}
